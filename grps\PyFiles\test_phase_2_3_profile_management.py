"""
Phase 2.3: Profile Management System Test
Test script to demonstrate the enhanced profile management capabilities
"""

import os
import sys
import json
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from updated_groups import EnhancedSeleniumBaseDriver, ProfileManager
    print("✅ Enhanced SeleniumBase Driver imported successfully")
except ImportError as e:
    print(f"❌ Failed to import enhanced driver: {str(e)}")
    sys.exit(1)


def test_profile_manager():
    """Test the ProfileManager class functionality"""
    print("\n🧪 Testing ProfileManager Class")
    print("=" * 50)
    
    # Initialize ProfileManager
    profile_manager = ProfileManager()
    print("✅ ProfileManager initialized")
    
    # Test profile creation
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    print("\n📁 Creating test profiles...")
    for email in test_emails:
        profile = profile_manager.create_profile(email)
        print(f"✅ Created profile for {email}: {profile['profile_id']}")
        
        # Verify fingerprint consistency
        fingerprint = profile_manager.get_profile_fingerprint(email)
        print(f"   🔒 Fingerprint seeds - Canvas: {fingerprint.get('canvas_seed')}, WebGL: {fingerprint.get('webgl_seed')}")
    
    # Test profile listing
    print("\n📋 Listing all profiles...")
    profiles = profile_manager.list_profiles()
    for profile_id, info in profiles.items():
        print(f"   📧 {info['email']} - Created: {info['created_at'][:19]} - Status: {info['status']}")
    
    # Test profile statistics
    print("\n📊 Profile statistics...")
    stats = profile_manager.get_profile_stats()
    print(f"   📈 Total profiles: {stats['total_profiles']}")
    print(f"   🟢 Active profiles: {stats['active_profiles']}")
    print(f"   💾 Storage used: {stats['total_storage_mb']} MB")
    
    # Test profile cleanup
    print("\n🧹 Testing profile cleanup...")
    cleaned = profile_manager.cleanup_old_profiles(days_threshold=0)  # Clean all for test
    print(f"   🗑️ Cleaned up {cleaned} profiles")
    
    return profile_manager


def test_enhanced_driver_with_profiles():
    """Test the EnhancedSeleniumBaseDriver with profile management"""
    print("\n🚗 Testing Enhanced Driver with Profile Management")
    print("=" * 60)
    
    test_email = "<EMAIL>"
    test_password = "test_password"
    test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    try:
        # Create driver instance
        print(f"📧 Creating driver for: {test_email}")
        driver = EnhancedSeleniumBaseDriver(test_email, test_password, test_ua, 1)
        
        # Test profile information
        print("\n📋 Profile Information:")
        profile_info = driver.get_profile_info()
        for key, value in profile_info.items():
            print(f"   {key}: {value}")
        
        # Test profile fingerprint
        print("\n🔒 Profile Fingerprint:")
        fingerprint = driver.get_profile_fingerprint()
        if 'error' not in fingerprint:
            print(f"   User Agent: {fingerprint.get('user_agent', 'N/A')[:50]}...")
            print(f"   Screen Resolution: {fingerprint.get('screen_resolution')}")
            print(f"   Timezone: {fingerprint.get('timezone')}")
            print(f"   Language: {fingerprint.get('language')}")
            print(f"   Hardware Concurrency: {fingerprint.get('hardware_concurrency')}")
        else:
            print(f"   ❌ Error: {fingerprint['error']}")
        
        # Test navigation with profile-specific fingerprints
        print("\n🌐 Testing navigation with profile-specific fingerprints...")
        try:
            driver.go("https://www.google.com")
            print("✅ Navigation successful")
            
            # Test fingerprint consistency
            print("🔍 Verifying fingerprint application...")
            user_agent = driver.execute_js("return navigator.userAgent;")
            screen_width = driver.execute_js("return screen.width;")
            screen_height = driver.execute_js("return screen.height;")
            language = driver.execute_js("return navigator.language;")
            
            print(f"   🌐 Applied User Agent: {user_agent[:50]}...")
            print(f"   📺 Applied Screen: {screen_width}x{screen_height}")
            print(f"   🗣️ Applied Language: {language}")
            
        except Exception as e:
            print(f"⚠️ Navigation test failed: {str(e)}")
        
        # Test profile management methods
        print("\n🛠️ Testing profile management methods...")
        
        # List all profiles
        all_profiles = driver.list_all_profiles()
        if 'error' not in all_profiles:
            print(f"   📊 Total profiles managed: {len(all_profiles)}")
        
        # Get profile stats
        stats = driver.get_profile_stats()
        if 'error' not in stats:
            print(f"   📈 Profile statistics: {stats['total_profiles']} profiles, {stats['total_storage_mb']} MB")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        driver.finish()
        print("✅ Driver cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced driver test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_profile_consistency():
    """Test that profiles maintain consistent fingerprints across sessions"""
    print("\n🔄 Testing Profile Consistency Across Sessions")
    print("=" * 55)
    
    test_email = "<EMAIL>"
    
    # Create first driver instance
    print("🚗 Creating first driver instance...")
    driver1 = EnhancedSeleniumBaseDriver(test_email, "password", "test_ua", 1)
    fingerprint1 = driver1.get_profile_fingerprint()
    driver1.finish()
    
    # Create second driver instance with same email
    print("🚗 Creating second driver instance with same email...")
    driver2 = EnhancedSeleniumBaseDriver(test_email, "password", "test_ua", 2)
    fingerprint2 = driver2.get_profile_fingerprint()
    driver2.finish()
    
    # Compare fingerprints
    print("\n🔍 Comparing fingerprints...")
    if fingerprint1.get('canvas_seed') == fingerprint2.get('canvas_seed'):
        print("✅ Canvas seeds match - Profile consistency maintained")
    else:
        print("❌ Canvas seeds don't match - Profile consistency failed")
    
    if fingerprint1.get('user_agent') == fingerprint2.get('user_agent'):
        print("✅ User agents match - Profile consistency maintained")
    else:
        print("❌ User agents don't match - Profile consistency failed")
    
    if fingerprint1.get('screen_resolution') == fingerprint2.get('screen_resolution'):
        print("✅ Screen resolutions match - Profile consistency maintained")
    else:
        print("❌ Screen resolutions don't match - Profile consistency failed")
    
    # Clean up test profile
    profile_manager = ProfileManager()
    profile_manager.remove_profile(test_email)
    print("🧹 Test profile cleaned up")


def demonstrate_phase_2_3():
    """Demonstrate Phase 2.3 Profile Management System capabilities"""
    print("🚀 Phase 2.3: Profile Management System Demonstration")
    print("=" * 65)
    
    print("\n📋 Phase 2.3 Features:")
    print("✅ Isolated browser profiles per account")
    print("✅ Profile persistence and cleanup")
    print("✅ Profile-specific fingerprints")
    print("✅ Consistent fingerprints across sessions")
    print("✅ Profile lifecycle management")
    
    # Run tests
    success_count = 0
    total_tests = 4
    
    try:
        # Test 1: ProfileManager
        profile_manager = test_profile_manager()
        success_count += 1
    except Exception as e:
        print(f"❌ ProfileManager test failed: {str(e)}")
    
    try:
        # Test 2: Enhanced Driver with Profiles
        if test_enhanced_driver_with_profiles():
            success_count += 1
    except Exception as e:
        print(f"❌ Enhanced Driver test failed: {str(e)}")
    
    try:
        # Test 3: Profile Consistency
        test_profile_consistency()
        success_count += 1
    except Exception as e:
        print(f"❌ Profile consistency test failed: {str(e)}")
    
    try:
        # Test 4: Integration Test
        print("\n🔗 Integration Test - Multiple Accounts")
        print("=" * 45)
        
        emails = ["<EMAIL>", "<EMAIL>"]
        drivers = []
        
        for email in emails:
            driver = EnhancedSeleniumBaseDriver(email, "password", "test_ua", len(drivers))
            drivers.append(driver)
            profile_info = driver.get_profile_info()
            print(f"✅ Created isolated profile for {email}: {profile_info.get('profile_id', 'N/A')}")
        
        # Cleanup
        for driver in drivers:
            driver.finish()
        
        success_count += 1
        print("✅ Integration test completed")
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
    
    # Summary
    print(f"\n📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 Phase 2.3 Profile Management System implementation successful!")
        print("✅ All profile management features are working correctly")
    else:
        print("⚠️ Some tests failed - review implementation")
    
    print("\n📝 Next Steps:")
    print("   • Integrate with existing workflows")
    print("   • Test with real Google Groups operations")
    print("   • Monitor profile storage usage")
    print("   • Implement profile backup/restore if needed")


if __name__ == "__main__":
    """Main execution"""
    try:
        demonstrate_phase_2_3()
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"❌ Demonstration failed: {str(e)}")
        import traceback
        traceback.print_exc()
